"use client";

import { useEffect, useState, type FC } from "react";
import { Media } from "@/components/render/render-media";
import type { Media as TMedia } from "@/payload-types";
import type { Page } from "@/payload-types";
import { randId } from "@/lib/utils";
import { Split } from "@/components/motion/split";

export const ChoirDirectorHero: FC<Page["hero"]> = ({
	content,
	title,
	directorImage,
	directorReferences,
}) => {
	const images = directorImage?.filter(
		(image) => typeof image === "object",
	) as TMedia[];

	const [currentImage, setCurrentImage] = useState<TMedia>(images[0]);

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		if (!currentImage) return;
		if (!images || images.length === 0) return;

		const interval = setInterval(() => {
			const currentIndex = images.indexOf(currentImage);
			const nextIndex = (currentIndex + 1) % images.length;
			setCurrentImage(images[nextIndex]);
		}, 300);

		return () => clearInterval(interval);
	}, [currentImage]);

	const sanitizeLinkAsLabel = (link: string) => {
		if (link.startsWith("https://")) return link.replace("https://", "");
		return link;
	};

	return (
		<section className="layout-block pt-16">
			<div className="default-grid">
				<Split delay={0.25} animationOnScroll={false}>
					<h1 className="col-span-full z-10 row-start-1">{title}</h1>
				</Split>
				<div className="col-span-6 col-start-7 row-start-1">
					<div className="relative overflow-hidden aspect-[4/3]">
						{images &&
							images.length > 0 &&
							images.map((image) => (
								<Media
									key={image.id}
									resource={image}
									style={{ opacity: image === currentImage ? 1 : 0 }}
									className="w-full h-full object-cover !absolute inset-0"
									imgClassName="w-full h-full object-cover"
								/>
							))}
					</div>
				</div>
				<Split delay={0.5} animationOnScroll={false}>
					<h3 className="col-span-8 indent-[25%]">{content}</h3>
				</Split>
				<div className="col-start-3 col-span-8 flex flex-col gap-4 pt-12">
					{directorReferences?.map((ref, i) => (
						<div className="grid grid-cols-8" key={ref.id}>
							<div className="col-span-5">
								<Split delay={0.5 + i * 0.15} animationOnScroll={false}>
									<p className="col-span-5">{ref.reference}</p>
								</Split>
							</div>
							<div className="col-span-3">
								<Split delay={0.5 + i * 0.15} animationOnScroll={false}>
									<a
										key={ref.id}
										href={ref.link}
										target="_blank"
										rel="noopener noreferrer"
										className="block underline font-bold"
									>
										{sanitizeLinkAsLabel(ref.link)}
									</a>
								</Split>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	);
};
