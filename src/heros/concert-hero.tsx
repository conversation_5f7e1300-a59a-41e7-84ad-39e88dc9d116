import type { <PERSON> } from "react";
import type { <PERSON> } from "@/payload-types";
import { Split } from "@/components/motion/split";

export const ConcertHero: FC<Page["hero"]> = ({ content, title }) => {
	return (
		<section className="layout-block">
			<div className="relative default-grid pt-12">
				<Split delay={0.25} animationOnScroll={false}>
					<h1 className="col-span-full">{title}</h1>
				</Split>
				<Split delay={0.5} animationOnScroll={false}>
					<p className="col-span-4 col-start-9 text-right">{content}</p>
				</Split>
			</div>
		</section>
	);
};
