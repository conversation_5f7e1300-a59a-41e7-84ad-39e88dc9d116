import type { <PERSON> } from "react";
import type { Page } from "@/payload-types";
import { ParallaxImage } from "@/components/motion/parallax-image";
import { Split } from "@/components/motion/split";

export const LowImpact: FC<Page["hero"]> = ({
	content,
	title,
	variant,
	image,
}) => {
	return (
		<section className="relative !mb-0">
			<div className="absolute inset-0 overflow-hidden">
				<ParallaxImage media={image} />
				{variant === "dimmed" && (
					<div className="absolute inset-0 bg-secondary/50" />
				)}
				{variant === "multiplied" && (
					<div className="absolute inset-0 bg-contrast mix-blend-multiply" />
				)}
			</div>
			<div className="relative layout-grid default-grid text-primary py-12">
				<Split delay={0.25} animationOnScroll={false}>
					<h1 className="col-span-full mb-[25svh]">{title}</h1>
				</Split>
				<Split delay={0.5} animationOnScroll={false}>
					<h3 className="content-end-safe col-span-7 col-start-6 indent-[25%]">
						{content}
					</h3>
				</Split>
			</div>
		</section>
	);
};
