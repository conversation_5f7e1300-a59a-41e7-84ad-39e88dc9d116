"use client";

import { useEffect, useState, type FC } from "react";
import type {
	Media as TMedia,
	ChoirDirectorBlock as ChoirDirectorBlockProps,
} from "@/payload-types";

import { cn, randId } from "@/lib/utils";
import RichText from "@/components/render/render-rich-text";
import { CMSLink } from "@/components/cms-link";
import { Media } from "@/components/render/render-media";

export const ChoirDirectorBlock: FC<ChoirDirectorBlockProps> = ({
	link,
	richText,
	images: imagesFromProps,
	overlapWithNext,
}) => {
	const images = imagesFromProps?.filter(
		(image) => typeof image === "object",
	) as TMedia[];
	const [currentImage, setCurrentImage] = useState<TMedia>(images[0]);

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		if (!currentImage) return;
		if (!images || images.length === 0) return;

		const interval = setInterval(() => {
			const currentIndex = images.indexOf(currentImage);
			const nextIndex = (currentIndex + 1) % images.length;
			setCurrentImage(images[nextIndex]);
		}, 300);

		return () => clearInterval(interval);
	}, [currentImage]);

	return (
		<section className={cn("layout-block", overlapWithNext && "!-mb-32")}>
			<div className="default-grid">
				<div className="col-span-8 h-full flex flex-col justify-center">
					<div className="pt-32 indent-[var(--width-columns-2)]">
						{richText && <RichText data={richText} enableGutter={false} />}
					</div>
					<div className="pl-[var(--width-columns-2)] mt-12">
						<CMSLink {...link} appearance="default" />
					</div>
				</div>
				<div className="col-span-4 col-start-9">
					<div className="relative overflow-hidden aspect-[3/4]">
						{images.map((image) => (
							<Media
								key={image.id}
								resource={image}
								style={{ opacity: image === currentImage ? 1 : 0 }}
								className="w-full h-full object-cover !absolute inset-0"
								imgClassName="w-full h-full object-cover"
							/>
						))}
					</div>
				</div>
			</div>
		</section>
	);
};
