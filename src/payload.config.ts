import { mongoose<PERSON>dapter } from "@payloadcms/db-mongodb";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import path from "node:path";
import { buildConfig } from "payload";
import { fileURLToPath } from "node:url";
import sharp from "sharp";
import { en } from "@payloadcms/translations/languages/en";
import { de } from "@payloadcms/translations/languages/de";

import * as Collections from "./collections";
import * as Plugins from "./plugins";
import { Header } from "./globals/Header";
import { Footer } from "./globals/Footer";

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

const urls = [
	"https://new.hard-chor.at",
	"http://localhost:3000",
	"https://*.hard-chor.at",
];

export default buildConfig({
	cors: urls,
	csrf: urls,
	serverURL: process.env.NEXT_PUBLIC_SERVER_URL,

	admin: {
		user: Collections.Users.slug,
		importMap: {
			baseDir: path.resolve(dirname),
		},
		components: {
			graphics: {
				Icon: "@/components/admin/icon#Icon",
				Logo: "@/components/admin/logo#Logo",
			},
		},
		autoLogin:
			process.env.PAYLOAD_AUTO_LOGIN === "true"
				? {
						email: process.env.PAYLOAD_AUTO_LOGIN_EMAIL || "admin@localhost",
						password: process.env.PAYLOAD_AUTO_LOGIN_PASSWORD || "admin",
						prefillOnly: true,
					}
				: undefined,
		livePreview: {
			collections: ["pages"],
			breakpoints: [
				{
					label: "Mobile",
					name: "mobile",
					width: 375,
					height: 667,
				},
				{
					label: "Tablet",
					name: "tablet",
					width: 768,
					height: 1024,
				},
				{
					label: "Desktop",
					name: "desktop",
					width: 1440,
					height: 900,
				},
			],
		},
	},
	i18n: {
		supportedLanguages: { de, en },
		fallbackLanguage: "de",
	},
	collections: Object.values(Collections),
	globals: [Header, Footer],
	editor: lexicalEditor({
		lexical: {
			namespace: "default",
			theme: {
				paragraph: "editor-paragraph",
			},
		},
	}),
	secret: process.env.PAYLOAD_SECRET || "",
	typescript: {
		outputFile: path.resolve(dirname, "payload-types.ts"),
	},
	db: mongooseAdapter({
		url: process.env.DATABASE_URI || "",
	}),
	sharp,
	plugins: Object.values(Plugins),
});
