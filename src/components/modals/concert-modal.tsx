"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { Share, X } from "lucide-react";
import Lenis from "lenis";
import { useEffect, useRef, useState } from "react";
import { useModal } from "@/hooks/use-modal";
import { Media } from "../render/render-media";
import RichText from "../render/render-rich-text";

export const ConcertModal = () => {
	const {
		isModalOpen,
		setIsOpen,
		data: { concert },
	} = useModal();

	const [copiedLink, setCopiedLink] = useState(false);

	const wrapperRef = useRef<HTMLDivElement | null>(null);
	const lenisRef = useRef<Lenis | null>(null);

	useEffect(() => {
		if (isModalOpen) {
			document.body.style.overflow = "hidden"; // Lock background scroll

			// Wait for the modal DOM to be present
			const timeout = setTimeout(() => {
				if (!wrapperRef.current) return;

				const content = wrapperRef.current.querySelector(
					"[data-lenis-content]",
				) as HTMLElement;
				if (!content) return;

				const lenis = new Lenis({
					wrapper: wrapperRef.current as HTMLElement,
					content: content,
					duration: 1.2,
					gestureOrientation: "vertical",
				});

				lenisRef.current = lenis;

				const raf = (time: number) => {
					lenis.raf(time);
					requestAnimationFrame(raf);
				};
				requestAnimationFrame(raf);
			}, 0); // defer to next tick

			return () => {
				clearTimeout(timeout);
				document.body.style.overflow = "";
				lenisRef.current?.destroy();
				lenisRef.current = null;
			};
		}

		document.body.style.overflow = "";
		lenisRef.current?.destroy();
		lenisRef.current = null;
	}, [isModalOpen]);

	const handleClose = () => {
		setIsOpen(false, { concert: concert });

		setTimeout(() => {
			if (concert) {
				setIsOpen(false, { concert: null });
			}
		}, 500);
	};

	const copyLink = () => {
		navigator.clipboard.writeText(window.location.href);
		setCopiedLink(true);
		setTimeout(() => {
			setCopiedLink(false);
		}, 2000);
	};

	return (
		<Dialog.Root open={isModalOpen} onOpenChange={handleClose}>
			<Dialog.Portal>
				<Dialog.Overlay className="fixed inset-0 bg-secondary/40 z-40 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
				<Dialog.Content className="DialogContent | layout-block fixed bottom-0 left-1/2 z-50 -translate-x-1/2 bg-primary h-[90svh] rounded-t-2xl shadow-xl overflow-hidden data-[state=open]:animate-modal-in data-[state=closed]:animate-modal-out">
					<Dialog.Close asChild>
						<button
							type="button"
							className="absolute top-0 right-0 cursor-pointer z-10"
						>
							<div className="size-40">
								<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-32">
									<span className="h-px w-full bg-secondary rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
									<span className="h-px w-full bg-secondary -rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
								</div>
								<p className="h-full w-full bg-contrast rounded-full rounded-tr-none grid place-items-center text-sm text-center">
									De Gaudi
									<br /> wieder <br />
									zuamochn
								</p>
							</div>
						</button>
					</Dialog.Close>
					<div
						ref={wrapperRef}
						data-lenis-wrapper
						className="h-full w-full overflow-hidden will-change-transform"
					>
						<div
							data-lenis-content
							className="min-h-full will-change-transform"
						>
							{concert && (
								<div className="grid grid-cols-2">
									<div className="relative pr-[12vw]">
										<Media
											resource={concert.image}
											className="aspect-[3/4] mb-8"
											imgClassName="object-cover h-full w-full"
										/>
									</div>
									<div className="px-6 pt-[12vw] pb-[6vw]">
										<div className="mb-8">
											<Dialog.Title asChild>
												<h3>
													<strong>{concert.title}</strong>
												</h3>
											</Dialog.Title>
											<Dialog.Description asChild>
												<p className="h3">{concert.subline}</p>
											</Dialog.Description>
										</div>
										<div className="grid [grid-template-columns:max-content_1fr] gap-x-4 gap-y-2">
											<p>
												<strong>WANN:</strong>
											</p>
											<p>{concert.formattedDateString}</p>
											<p>
												<strong>WO:</strong>
											</p>
											<p>{concert.where}</p>
											<p>
												<strong>WER:</strong>
											</p>
											<p>{concert.who}</p>
											<p>
												<strong>LEITUNG:</strong>
											</p>
											<p>{concert.lead}</p>
										</div>
										<div className="mt-8">
											<button
												type="button"
												onClick={copyLink}
												className="flex items-center gap-x-2 font-semibold cursor-pointer uppercase"
											>
												<svg
													xmlns="http://www.w3.org/2000/svg"
													width="32"
													height="32"
													viewBox="0 0 24 24"
												>
													{/* Icon from Google Material Icons by Material Design Authors - https://github.com/material-icons/material-icons/blob/master/LICENSE */}
													<title>share</title>
													<path
														fill="currentColor"
														d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81c1.66 0 3-1.34 3-3s-1.34-3-3-3s-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65c0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92"
													/>
												</svg>
												{copiedLink ? "Kopiert" : "Teilen"}
											</button>
										</div>
										<div className="mt-12">
											<h3 className="mb-4">
												<strong>Programm:</strong>
											</h3>
											{concert.program && <RichText data={concert.program} />}
										</div>
									</div>
								</div>
							)}
						</div>
					</div>
				</Dialog.Content>
			</Dialog.Portal>
		</Dialog.Root>
	);
};
