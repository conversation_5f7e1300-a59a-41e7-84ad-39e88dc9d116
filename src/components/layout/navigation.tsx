"use client";

import { useRef, type FC } from "react";
import gsap from "gsap";
import type { Concert, Header, Page } from "@/payload-types";
import { CMSLink } from "../cms-link";
import { MegaNav } from "./mega-nav";
import { useModal } from "@/hooks/use-modal";
import { useGSAP } from "@gsap/react";
import { usePathname } from "next/navigation";
import Link from "next/link";

interface INavigationProps {
	header: Header;
	nextConcert: Concert;
}

export const Navigation: FC<INavigationProps> = ({ header, nextConcert }) => {
	const navRef = useRef<HTMLDivElement>(null);
	const logoRef = useRef<HTMLImageElement>(null);
	const logoTextRef = useRef<HTMLSpanElement>(null);
	const pathname = usePathname();

	const { setIsOpen } = useModal();

	useGSAP(
		() => {
			if (pathname !== "/" && pathname !== "/home") return;

			gsap.set(logoRef.current, { scale: 18, y: "00%" });
			gsap.set(logoTextRef.current, { y: "600%", x: "500%" });

			gsap.to(logoRef.current, {
				scale: 2,
				y: "0%",
				scrollTrigger: {
					trigger: document.body,
					start: "top top",
					end: "+=500px",
					scrub: true,
				},
			});
			gsap.to(logoTextRef.current, {
				x: "0%",
				y: "0%",
				scrollTrigger: {
					trigger: document.body,
					start: "top top",
					end: "+=500px",
					scrub: true,
				},
			});
		},
		{ scope: navRef },
	);

	return (
		<>
			<header className="fixed inset-x-0 top-0 z-50 layout-block">
				<div className="default-grid py-safe items-start" ref={navRef}>
					<div className="col-span-4 relative pl-24 origin-top-left">
						<span ref={logoTextRef} className="inline-block">
							<CMSLink
								type="reference"
								reference={{
									relationTo: "pages",
									value: { slug: "home" } as Page,
								}}
								className="cursor-pointer"
							>
								<span className="font-bold text-3xl">Hard-Chor</span>
							</CMSLink>
						</span>
						<img
							ref={logoRef}
							src="/logo.svg"
							alt="logo"
							className="pointer-events-none absolute -top-4 -left-4  w-auto h-full origin-top-left scale-200"
						/>
					</div>
					<nav className="col-span-8 col-start-5 flex justify-end">
						<Link
							href={"/konzert/nächstes}"}
							className="relative w-[160px] overflow-hidden text-xs px-2 bg-secondary text-contrast font-bold uppercase cursor-pointer hover:bg-contrast hover:text-secondary transition-colors ease-out-expo duration-500"
						>
							<div className="animate-marquee whitespace-nowrap">
								Nächstes Konzert - Nächstes Konzert - Nächstes Konzert -
							</div>
						</Link>
					</nav>
				</div>
			</header>
			<nav className="fixed inset-x-0 top-0 z-50 layout-block default-grid py-safe items-start mix-blend-difference text-primary pointer-events-none">
				<ul className="flex items-center gap-x-4 col-span-8 col-start-5">
					{header.navItems?.map((item) => {
						if (item.isMegaNav) {
							return (
								<li
									key={item.id}
									className="text-sm relative pointer-events-auto"
								>
									<MegaNav item={item} navRef={navRef}>
										{item.label}
									</MegaNav>
								</li>
							);
						}

						return (
							<li key={item.id} className="text-sm pointer-events-auto">
								<CMSLink {...item.link} appearance="underline" />
							</li>
						);
					})}
				</ul>
			</nav>
		</>
	);
};
