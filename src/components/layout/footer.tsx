"use client";

import type { <PERSON> } from "react";
import Link from "next/link";
import type { Footer as TFooter } from "@/payload-types";
import { CMSLink } from "../cms-link";

interface IFooterProps {
	footer: TFooter;
}

export const Footer: FC<IFooterProps> = ({ footer }) => {
	return (
		<>
			<section className="relative !-mt-32 pointer-events-none">
				<div className="scale-[-1.25] translate-x-1/6">
					<img src="/swirl.svg" alt="swirl" className="w-full h-auto" />
				</div>
			</section>
			<footer className="layout-block pb-8">
				<div className="default-grid">
					<div className="col-span-2">logo</div>
					{footer.navItems?.map((item) => (
						<div className="col-span-1" key={item.id}>
							{!item.hasChildren ? (
								<CMSLink
									{...item.link}
									className="hover:underline underline-offset-2"
								/>
							) : (
								<>
									<span className="mb-4 inline-block">{item.label}</span>
									<ul>
										{item.children?.map((child) => (
											<li className="mb-4 text-sm" key={child.id}>
												<CMSLink
													{...child.link}
													className="hover:underline whitespace-nowrap"
												/>
											</li>
										))}
									</ul>
								</>
							)}
						</div>
					))}
				</div>
				<div className="default-grid mt-32">
					<div className="col-span-2" />
					{footer.bottomNavItems?.map((item) => (
						<div className="col-span-1" key={item.id}>
							<CMSLink
								{...item.link}
								className="whitespace-nowrap hover:underline"
							/>
						</div>
					))}
				</div>
			</footer>
		</>
	);
};
