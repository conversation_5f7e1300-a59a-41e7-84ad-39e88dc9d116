"use client";

import { type FC, useEffect, useRef, useState } from "react";
import gsap from "gsap";
import { CMSLink } from "../cms-link";
import type { Header } from "@/payload-types";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useGSAP } from "@gsap/react";
import { createPortal } from "react-dom";

interface MegaNavProps {
	navRef: React.RefObject<HTMLDivElement | null>;
	item: NonNullable<NonNullable<Header["navItems"]>[number]>;
	children: React.ReactNode;
}

export const MegaNav: FC<MegaNavProps> = ({ children, navRef, item }) => {
	const [mounted, setMounted] = useState(false);
	const [isAnimating, setIsAnimating] = useState(false);
	const pathname = usePathname();

	const menuRef = useRef(null);
	const menuOverlayRef = useRef<HTMLDivElement>(null);
	const menuItemsRef = useRef(null);

	const menuBtnRef = useRef(null);
	const closeBtnRef = useRef(null);

	useEffect(() => {
		setMounted(true);
	}, []);

	useGSAP(
		() => {
			gsap.set(menuOverlayRef.current, {
				opacity: 0,
				pointerEvents: "none",
			});
			gsap.set(closeBtnRef.current, { y: "100%" });
			gsap.set(".menu-overlay-items a", { y: "-100%" });
		},
		{ scope: menuRef },
	);

	const openMenu = () => {
		if (isAnimating) return;
		setIsAnimating(true);

		const tl = gsap.timeline({ onComplete: () => setIsAnimating(false) });
		tl.to(menuBtnRef.current, {
			y: "-100%",
			duration: 0.5,
			ease: "power3.out",
			onComplete: () => {
				if (navRef.current) {
					navRef.current.style.pointerEvents = "none";
				}
				gsap.set(menuBtnRef.current, { y: "100%" });
			},
		})
			.to(
				menuOverlayRef.current,
				{
					opacity: 1,
					duration: 0.5,
					ease: "power3.out",
					onStart: () => {
						if (menuOverlayRef.current) {
							menuOverlayRef.current.style.pointerEvents = "all";
						}
					},
				},
				"-=0.45",
			)
			.to(
				closeBtnRef.current,
				{ y: "0%", duration: 1, ease: "power3.out" },
				"-=0.5",
			)
			.to(
				".menu-overlay-items a",
				{
					y: "0%",
					duration: 1,
					stagger: 0.075,
					ease: "power3.out",
				},
				"<",
			);
	};

	const closeMenu = () => {
		if (isAnimating) return;
		setIsAnimating(true);

		const tl = gsap.timeline({ onComplete: () => setIsAnimating(false) });
		tl.to(closeBtnRef.current, {
			y: "-100%",
			duration: 0.5,
			ease: "power3.out",
		})
			.to(
				".menu-overlay-items a",
				{
					y: "-130%",
					duration: 0.5,
					stagger: 0.05,
					ease: "power3.in",
				},
				"<",
			)
			.to(
				menuOverlayRef.current,
				{
					opacity: 0,
					duration: 0.5,
					ease: "power3.out",
					onComplete: () => {
						if (menuOverlayRef.current) {
							menuOverlayRef.current.style.pointerEvents = "none";
						}
						gsap.set(closeBtnRef.current, { y: "100%" });
						gsap.set(".menu-overlay-items .revealer h3", { y: "100%" });
						gsap.set(".menu-footer .revealer p, .menu-footer .revealer h3", {
							y: "100%",
						});
					},
				},
				"+=0.1",
			)
			.to(
				menuBtnRef.current,
				{
					y: "0%",
					duration: 0.5,
					ease: "power3.out",
					onStart: () => {
						if (navRef.current) {
							navRef.current.style.pointerEvents = "all";
						}
					},
				},
				"-=0.45",
			);
	};

	return (
		<>
			<button
				type="button"
				className="variant--underline group relative w-max cursor-pointer"
				onClick={openMenu}
			>
				<p className="text-sm">{children}</p>

				{/* Contrast Circle */}
				<span className="absolute top-0 -right-2 -translate-y-1/2 h-2 w-2 bg-[#673DC3] text-secondary/0 text-[8px] font-medium rounded-full flex items-center justify-center overflow-hidden transition-all duration-300 ease-in-out group-hover:w-8 group-hover:text-secondary/100">
					MENU
				</span>
			</button>
			{mounted &&
				createPortal(
					<div
						ref={menuRef}
						className="fixed top-0 left-0 w-screen h-[100svh] pointer-events-none z-[10000]"
					>
						<div
							ref={menuOverlayRef}
							className="fixed top-0 left-0 w-screen h-[100svh] overflow-hidden text-secondary will-change-[opacity] pointer-events-none z-20 opacity-0 bg-secondary"
						>
							<button
								type="button"
								className="fixed bottom-6 right-6 cursor-pointer [clip-path:polygon(0_0,100%_0,100%_100%,0_100%)]"
								onClick={closeMenu}
							>
								<div ref={closeBtnRef} className="size-40">
									<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-32">
										<span className="h-px w-full bg-secondary rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
										<span className="h-px w-full bg-secondary -rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
									</div>
									<p className="h-full w-full bg-contrast rounded-full grid place-items-center text-sm text-center">
										De Gaudi
										<br /> wieder <br />
										zuamochn
									</p>
								</div>
							</button>

							<div
								ref={menuItemsRef}
								className="menu-overlay-items text-contrast group px-6 pb-6 pt-2 flex flex-col absolute bottom-0 left-0 w-max"
							>
								{item.children?.map((item) => {
									const path = `/${
										item.link.type === "reference"
											? typeof item.link.reference?.value === "object"
												? item.link.reference?.value?.slug
												: item.link.url
											: item.link.url
									}`;

									return (
										<div
											key={item.id}
											className={cn(
												"relative w-max [clip-path:polygon(0_0,100%_0,200%_100%,0_200%)] opacity-100 group-hover:opacity-50 hover:opacity-100 transition-opacity duration-300",
												path === pathname && "!opacity-25",
											)}
										>
											<CMSLink
												{...item.link}
												label={null}
												className={cn(
													"mega-nav-text | inline-block",
													path === pathname && "!cursor-default",
												)}
											>
												{item.link.label}
											</CMSLink>
										</div>
									);
								})}
							</div>
							<div className="absolute right-0 top-0 bottom-0 scale-125 -z-10">
								<div className="punk-clip | aspect-[467/640] h-full">
									<img
										src="/images/The_Hustle_Wahl_High_Res_Ret-5821-II_web_II.jpg"
										alt="punk"
										className="w-full h-full object-cover"
									/>
								</div>
							</div>
						</div>
					</div>,
					document.getElementById("meganav-root") as HTMLElement,
				)}
		</>
	);
};
