import type React from "react";

import type { Page } from "@/payload-types";
import { HighImpact } from "@/heros/high-impact";
import { LowImpact } from "@/heros/low-impact";
import { ConcertHero } from "@/heros/concert-hero";
import { AboutHero } from "@/heros/about-hero";
import { ChoirDirectorHero } from "@/heros/choir-direactor-hero";
import { ContactHero } from "@/heros/contact-hero";

const heroes = {
	"high-impact": HighImpact,
	"low-impact": LowImpact,
	"concert-hero": <PERSON><PERSON><PERSON>,
	"about-hero": <PERSON><PERSON><PERSON>,
	"choir-director-hero": <PERSON><PERSON><PERSON><PERSON>H<PERSON>,
	"contact-hero": ContactHero,
};

export const RenderHero: React.FC<Page["hero"]> = (props) => {
	const { type } = props || {};

	if (!type || type === "none") return null;

	const HeroToRender = heroes[type];

	if (!HeroToRender) return null;

	return <HeroToRender {...props} />;
};
