import type { <PERSON><PERSON><PERSON> } from "next";

import { Redirects } from "@/components/layout/redirects";
import configPromise from "@payload-config";
import { draftMode } from "next/headers";
import { getPayload } from "payload";
import { cache } from "react";

import { Footer } from "@/components/layout/footer";
import { Navigation } from "@/components/layout/navigation";
import { Wrapper } from "@/components/layout/wrapper";
import { RenderBlocks } from "@/components/render/render-blocks";
import { RenderHero } from "@/components/render/render-hero";
import { generateMeta } from "@/utils/gen-meta";
import { PageClient } from "./page.client";

export async function generateStaticParams() {
	const payload = await getPayload({ config: configPromise });
	const pages = await payload.find({
		collection: "pages",
		draft: false,
		limit: 1000,
		overrideAccess: false,
		pagination: false,
		select: { slug: true },
	});

	const params = pages.docs
		?.filter((doc) => doc.slug !== "home")
		.map(({ slug }) => ({ slug: slug?.split("/") }));

	return params;
}

type Args = {
	params: Promise<{ slug?: string[] }>;
};

export default async function Page({ params: paramsPromise }: Args) {
	const { slug: slugArray = [] } = await paramsPromise;
	const slug = slugArray.join("/") || "home";

	const url = `/${slug}`;
	const page = await queryPage({
		slug: slug || "home",
	});

	const nextConcert = await queryNextConcert();

	const { header, footer } = await queryGlobals();

	if (!page) {
		return <Redirects url={url} />;
	}

	const { hero, layout } = page;

	return (
		<>
			<Redirects disableNotFound url={url} />
			<Navigation header={header} nextConcert={nextConcert} />
			<Wrapper>
				<PageClient />
				<RenderHero {...hero} />
				<RenderBlocks blocks={layout} />
			</Wrapper>
			<Footer footer={footer} />
		</>
	);
}

export async function generateMetadata({ params }: Args): Promise<Metadata> {
	const { slug: slugArray = [] } = await params;
	const slug = slugArray.join("/") || "home";

	const page = await queryPage({ slug }); // now a string ✅

	return generateMeta({ doc: page });
}

const queryGlobals = cache(async () => {
	const payload = await getPayload({ config: configPromise });

	const header = await payload.findGlobal({
		slug: "header",
	});

	const footer = await payload.findGlobal({
		slug: "footer",
	});

	return {
		header,
		footer,
	};
});

const queryPage = cache(async ({ slug }: { slug: string }) => {
	const { isEnabled: draft } = await draftMode();

	const payload = await getPayload({ config: configPromise });

	const result = await payload.find({
		collection: "pages",
		draft,
		limit: 1,
		overrideAccess: draft,
		where: {
			slug: {
				equals: slug,
			},
		},
	});

	return result.docs?.[0] || null;
});

const queryNextConcert = cache(async () => {
	const payload = await getPayload({ config: configPromise });

	const result = await payload.find({
		collection: "concerts",
		depth: 1,
		where: {
			"dates.date": {
				greater_than_equal: new Date().toISOString(),
			},
		},
		sort: "dates.date",
		limit: 1,
	});

	return result.docs?.[0] || null;
});
