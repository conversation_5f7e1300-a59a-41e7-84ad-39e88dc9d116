"use client";

import { RefreshRouteOnSave as PayloadLivePreview } from "@payloadcms/live-preview-react";
import { useRevealer } from "@/hooks/use-revealer";
import type { FC } from "react";
import { useRouter } from "next/navigation";
import { ConcertModal } from "@/components/modals/concert-modal";

export const PageClient: FC = () => {
	const router = useRouter();
	useRevealer();

	return (
		<>
			<ConcertModal />
			<PayloadLivePreview
				refresh={() => router.refresh()}
				serverURL={process.env.NEXT_PUBLIC_BASE_URL || ""}
			/>
		</>
	);
};
