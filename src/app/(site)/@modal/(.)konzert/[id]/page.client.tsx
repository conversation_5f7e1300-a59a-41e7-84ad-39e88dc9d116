"use client";

import { useEffect, useRef, useState, type FC } from "react";
import type { Concert } from "@/payload-types";
import { RichText } from "@payloadcms/richtext-lexical/react";
import { Dialog } from "@radix-ui/react-dialog";
import Lenis from "lenis";
import { Media } from "@/components/render/render-media";

interface IConcertDetailModalPageClientProps {
	concert: Concert;
}

export const ConcertDetailModalPageClient: FC<
	IConcertDetailModalPageClientProps
> = ({ concert }) => {
	const [copiedLink, setCopiedLink] = useState(false);

	const wrapperRef = useRef<HTMLDivElement | null>(null);
	const lenisRef = useRef<Lenis | null>(null);

	useEffect(() => {
		document.body.style.overflow = "hidden";

		const timeout = setTimeout(() => {
			if (!wrapperRef.current) return;

			const content = wrapperRef.current.querySelector(
				"[data-lenis-content]",
			) as HTMLElement;
			if (!content) return;

			const lenis = new Lenis({
				wrapper: wrapperRef.current as HTMLElement,
				content: content,
				duration: 1.2,
				gestureOrientation: "vertical",
			});

			lenisRef.current = lenis;

			const raf = (time: number) => {
				lenis.raf(time);
				requestAnimationFrame(raf);
			};
			requestAnimationFrame(raf);
		}, 0);

		return () => {
			clearTimeout(timeout);
			lenisRef.current?.destroy();
			lenisRef.current = null;
		};
	}, []);

	const copyLink = () => {
		navigator.clipboard.writeText(window.location.href);
		setCopiedLink(true);
		setTimeout(() => {
			setCopiedLink(false);
		}, 2000);
	};

	return (
		<div
			ref={wrapperRef}
			data-lenis-wrapper
			className="h-full w-full overflow-hidden will-change-transform"
		>
			<div data-lenis-content className="min-h-full will-change-transform">
				{concert && (
					<div className="grid grid-cols-2">
						<div className="relative pr-[12vw]">
							<Media
								resource={concert.image}
								className="aspect-[3/4] mb-8"
								imgClassName="object-cover h-full w-full"
							/>
						</div>
						<div className="px-6 pt-[12vw] pb-[6vw]">
							<div className="mb-8">
								<h3>
									<strong>{concert.title}</strong>
								</h3>

								<p className="h3">{concert.subline}</p>
							</div>
							<div className="grid [grid-template-columns:max-content_1fr] gap-x-4 gap-y-2">
								<p>
									<strong>WANN:</strong>
								</p>
								<p>{concert.formattedDateString}</p>
								<p>
									<strong>WO:</strong>
								</p>
								<p>{concert.where}</p>
								<p>
									<strong>WER:</strong>
								</p>
								<p>{concert.who}</p>
								<p>
									<strong>LEITUNG:</strong>
								</p>
								<p>{concert.lead}</p>
							</div>
							<div className="mt-8">
								<button
									type="button"
									onClick={copyLink}
									className="flex items-center gap-x-2 font-semibold cursor-pointer uppercase"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="32"
										height="32"
										viewBox="0 0 24 24"
									>
										{/* Icon from Google Material Icons by Material Design Authors - https://github.com/material-icons/material-icons/blob/master/LICENSE */}
										<title>share</title>
										<path
											fill="currentColor"
											d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81c1.66 0 3-1.34 3-3s-1.34-3-3-3s-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65c0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92"
										/>
									</svg>
									{copiedLink ? "Kopiert" : "Teilen"}
								</button>
							</div>
							<div className="mt-12">
								<h3 className="mb-4">
									<strong>Programm:</strong>
								</h3>
								{concert.program && <RichText data={concert.program} />}
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};
