import type { Concert } from "@/payload-types";
import { create } from "zustand";

interface IUseModalStore {
	data: {
		concert: Concert | null;
	};
	isModalOpen: boolean;
	setIsOpen: (value: boolean, data: IUseModalStore["data"]) => void;
}

export const useModal = create<IUseModalStore>((set) => ({
	data: { concert: null },
	isModalOpen: false,
	setIsOpen: (value: boolean, data: IUseModalStore["data"]) =>
		set({ isModalOpen: value, data }),
}));
